<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>调试删除功能</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">调试删除功能</h1>
    
    <div class="bg-white p-6 rounded-lg shadow">
      <button id="add-btn" class="mb-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
        添加材料项
      </button>
      
      <div id="container" class="space-y-2">
        <!-- 材料项将在这里添加 -->
      </div>
      
      <div class="mt-4 text-lg font-semibold">
        总数量: <span id="count">0</span>
      </div>
    </div>
  </div>

  <script>
    let itemCount = 0;

    function addItem() {
      const container = document.getElementById('container');
      itemCount++;
      
      const item = document.createElement('div');
      item.className = 'flex items-center gap-4 p-3 bg-gray-50 rounded border';
      item.innerHTML = `
        <span class="flex-1">材料项 ${itemCount}</span>
        <button class="delete-btn px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600" data-id="${itemCount}">
          删除
        </button>
      `;
      
      container.appendChild(item);
      
      // 立即绑定删除事件
      const deleteBtn = item.querySelector('.delete-btn');
      deleteBtn.addEventListener('click', function(e) {
        console.log('删除按钮被点击，ID:', this.dataset.id);
        
        if (container.children.length > 1) {
          item.remove();
          updateCount();
          alert('已删除材料项 ' + this.dataset.id);
        } else {
          alert('至少需要保留一个材料项');
        }
      });
      
      updateCount();
    }
    
    function updateCount() {
      const count = document.getElementById('container').children.length;
      document.getElementById('count').textContent = count;
    }
    
    // 绑定添加按钮
    document.getElementById('add-btn').addEventListener('click', addItem);
    
    // 初始添加一个项目
    addItem();
  </script>
</body>
</html>
