const { app, BrowserWindow, ipcMain, dialog } = require('electron')
const path = require('path')
const fs = require('fs')

// 新的模块化导入
const dbConnection = require('./src/main/database/connection')
const migrations = require('./src/main/database/migrations')
const OrderService = require('./src/main/services/OrderService')
const ProductService = require('./src/main/services/ProductService')

// 仅在开发环境中启用热重载
if (!app.isPackaged) {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

// 保持对窗口对象的全局引用，避免JavaScript对象被垃圾回收时窗口关闭
let mainWindow

async function createWindow() {
  try {
    // 初始化数据库连接
    await dbConnection.initialize();
    console.log('数据库连接初始化成功');

    // 运行数据库迁移
    await migrations.runMigrations();
    console.log('数据库迁移完成');

    // 创建浏览器窗口
    mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        contextIsolation: true,
        nodeIntegration: false,
        enableRemoteModule: false
      }
    })

    // 移除菜单栏
    mainWindow.setMenu(null)

    // 加载应用的index.html
    mainWindow.loadFile('index.html')

    // 开发环境下打开开发者工具
    if (!app.isPackaged) {
      mainWindow.webContents.openDevTools()
    }

    // 当窗口关闭时关闭数据库连接
    mainWindow.on('closed', async () => {
      try {
        await dbConnection.close();
        console.log('数据库连接已关闭');
      } catch (err) {
        console.error('关闭数据库连接时出错:', err);
      }
      mainWindow = null;
    });
  } catch (err) {
    console.error('初始化应用时出错:', err);
    app.quit();
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(createWindow)

// 除了macOS外，当所有窗口都被关闭的时候退出程序
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit()
})

app.on('activate', function () {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，通常在应用程序中重新创建一个窗口
  if (BrowserWindow.getAllWindows().length === 0) createWindow()
})

// 处理获取所有订单的请求
ipcMain.handle('get-all-orders', async (event, options = {}) => {
  return await OrderService.getAllOrders(options);
});

// 处理获取单个订单的请求
ipcMain.handle('get-order', async (event, orderId) => {
  return await OrderService.getOrderById(orderId);
});

// 处理搜索订单的请求
ipcMain.handle('search-orders', async (event, criteria) => {
  return await OrderService.searchOrders(criteria);
});

// 处理添加订单的请求
ipcMain.handle('add-order', async (event, orderData) => {
  return await OrderService.createOrder(orderData);
});

// 处理更新订单的请求
ipcMain.handle('update-order', async (event, orderData) => {
  const orderId = orderData.id;
  return await OrderService.updateOrder(orderId, orderData);
});

// 处理删除订单的请求
ipcMain.handle('delete-order', async (event, orderId) => {
  return await OrderService.deleteOrder(orderId);
});

// 处理获取订单统计的请求
ipcMain.handle('get-order-statistics', async (event, criteria = {}) => {
  return await OrderService.getOrderStatistics(criteria);
});

// 处理获取今日统计的请求
ipcMain.handle('get-today-statistics', async () => {
  return await OrderService.getTodayStatistics();
});

// 处理搜索材料类型的请求
ipcMain.handle('search-material-types', async (event, materialName) => {
  return await OrderService.searchMaterialTypes(materialName);
});

// 处理获取所有材料类型的请求
ipcMain.handle('get-all-material-types', async () => {
  return await OrderService.getAllMaterialTypes();
});

// 处理CSV导出
ipcMain.handle('export-csv', async (event, csvContent) => {
  try {
    // 打开保存文件对话框
    const { canceled, filePath } = await dialog.showSaveDialog({
      title: '导出订单数据',
      defaultPath: `MatBook订单数据_${new Date().toISOString().split('T')[0]}.csv`,
      filters: [
        { name: 'CSV文件', extensions: ['csv'] }
      ]
    })

    if (canceled || !filePath) {
      return { success: false, message: '导出已取消' }
    }

    // 添加UTF-8 BOM以确保Excel等软件正确识别中文字符
    // UTF-8 BOM: EF BB BF
    const utf8BOM = Buffer.from([0xEF, 0xBB, 0xBF])
    const csvBuffer = Buffer.from(csvContent, 'utf-8')
    const finalBuffer = Buffer.concat([utf8BOM, csvBuffer])

    // 写入文件
    fs.writeFileSync(filePath, finalBuffer)

    return { success: true, message: `文件已成功导出至: ${filePath}` }
  } catch (error) {
    console.error('导出CSV失败:', error)
    return { success: false, message: `导出失败: ${error.message}` }
  }
})

// 处理获取应用版本
ipcMain.handle('get-app-version', async () => {
  try {
    const packageJson = require('./package.json')
    return { success: true, version: packageJson.version }
  } catch (error) {
    console.error('获取应用版本失败:', error)
    return { success: false, message: error.message }
  }
})

// 处理获取平台信息
ipcMain.handle('get-platform', async () => {
  try {
    return {
      success: true,
      platform: process.platform,
      arch: process.arch,
      nodeVersion: process.version
    }
  } catch (error) {
    console.error('获取平台信息失败:', error)
    return { success: false, message: error.message }
  }
})

// ==================== 商品相关的IPC处理器 ====================

// 处理获取所有商品的请求
ipcMain.handle('get-all-products', async (event, options = {}) => {
  return await ProductService.getAllProducts(options);
});

// 处理获取单个商品的请求
ipcMain.handle('get-product', async (event, productId) => {
  return await ProductService.getProductById(productId);
});

// 处理搜索商品的请求
ipcMain.handle('search-products', async (event, criteria) => {
  return await ProductService.searchProducts(criteria);
});

// 处理添加商品的请求
ipcMain.handle('add-product', async (event, productData) => {
  return await ProductService.createProduct(productData);
});

// 处理更新商品的请求
ipcMain.handle('update-product', async (event, productData) => {
  const productId = productData.id;
  return await ProductService.updateProduct(productId, productData);
});

// 处理删除商品的请求
ipcMain.handle('delete-product', async (event, productId) => {
  return await ProductService.deleteProduct(productId);
});

// 处理获取商品分类的请求
ipcMain.handle('get-product-categories', async () => {
  return await ProductService.getProductCategories();
});

// 处理获取商品统计的请求
ipcMain.handle('get-product-statistics', async () => {
  return await ProductService.getProductStatistics();
});







