<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试删除功能</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    .form-control {
      width: 100%;
      padding: 0.5rem 0.75rem;
      border: 1px solid #d1d5db;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      line-height: 1.25rem;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
      outline: none;
      border-color: #4f46e5;
      box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    }
  </style>
</head>
<body class="bg-gray-100">
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">测试材料清单删除功能</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-900">材料清单</h2>
        <button id="add-item-btn" type="button" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
          添加材料项
        </button>
      </div>

      <!-- 材料项表头 -->
      <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
        <div class="col-span-3">材料名称</div>
        <div class="col-span-2">类型</div>
        <div class="col-span-2 text-center">数量</div>
        <div class="col-span-2 text-center">单价 (¥)</div>
        <div class="col-span-2 text-center">金额 (¥)</div>
        <div class="col-span-1 text-center">操作</div>
      </div>

      <!-- 材料项容器 -->
      <div id="order-items-container" class="space-y-3">
        <!-- 材料项将通过JavaScript动态添加 -->
      </div>
      
      <!-- 总金额显示 -->
      <div class="mt-6 text-right">
        <span class="text-lg font-semibold">总金额：</span>
        <span id="order-total-amount" class="text-xl font-bold text-green-600">¥0.00</span>
      </div>
    </div>
  </div>

  <script>
    // 模拟 showError 和 showSuccess 函数
    function showError(message) {
      alert('错误: ' + message);
    }
    
    function showSuccess(message) {
      alert('成功: ' + message);
    }

    /**
     * 添加订单材料项
     */
    function addOrderItem() {
      const container = document.getElementById('order-items-container');
      if (!container) {
        console.error('Order items container not found');
        return;
      }

      const itemRow = document.createElement('div');
      itemRow.className = 'grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200';

      itemRow.innerHTML = `
        <div class="col-span-3">
          <input type="text" class="form-control item-name" placeholder="输入材料名称" required>
        </div>
        <div class="col-span-2 relative">
          <input type="text" class="form-control item-type" placeholder="选择或输入类型">
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-quantity text-center" value="1" min="0" step="1" required>
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-price text-center" value="0" min="0" step="0.01" required>
        </div>
        <div class="col-span-2 text-right">
          <span class="item-amount text-lg font-semibold text-gray-900">¥0.00</span>
        </div>
        <div class="col-span-1 text-center">
          <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      `;

      container.appendChild(itemRow);

      // 添加事件监听器
      const quantityInput = itemRow.querySelector('.item-quantity');
      const priceInput = itemRow.querySelector('.item-price');
      const deleteButton = itemRow.querySelector('.delete-item');

      // 数量和价格变化时计算金额
      if (quantityInput) {
        quantityInput.addEventListener('input', () => calculateItemAmount(itemRow));
      }
      if (priceInput) {
        priceInput.addEventListener('input', () => calculateItemAmount(itemRow));
      }

      // 删除按钮点击时删除行
      if (deleteButton) {
        deleteButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('删除按钮被点击');
          
          if (container.children.length > 1) {
            itemRow.remove();
            calculateOrderTotal();
            showSuccess('材料项已删除');
          } else {
            showError('订单至少需要一个材料项');
          }
        });
      }

      // 初始计算金额
      calculateItemAmount(itemRow);
    }

    /**
     * 计算单个材料项的金额
     */
    function calculateItemAmount(row) {
      const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
      const price = parseFloat(row.querySelector('.item-price').value) || 0;
      const amount = quantity * price;

      row.querySelector('.item-amount').textContent = `¥${amount.toFixed(2)}`;

      // 重新计算订单总金额
      calculateOrderTotal();
    }

    /**
     * 计算订单总金额
     */
    function calculateOrderTotal() {
      let total = 0;

      // 遍历所有材料项，累加金额
      document.querySelectorAll('#order-items-container .item-amount').forEach(element => {
        const amountText = element.textContent;
        const amount = parseFloat(amountText.replace('¥', '')) || 0;
        total += amount;
      });

      // 更新总金额显示
      const totalElement = document.getElementById('order-total-amount');
      if (totalElement) {
        totalElement.textContent = `¥${total.toFixed(2)}`;
      }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 绑定添加材料项按钮
      const addItemBtn = document.getElementById('add-item-btn');
      if (addItemBtn) {
        addItemBtn.addEventListener('click', addOrderItem);
      }

      // 添加一个初始材料项
      addOrderItem();
    });
  </script>
</body>
</html>
