<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>独立删除功能测试</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100">
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6 text-center">独立删除功能测试</h1>
    
    <div class="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-900">
          <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          材料清单
        </h2>
        <button id="add-item-btn" type="button" class="add-material-btn-enhanced">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加材料项
        </button>
      </div>

      <!-- 材料项表头 -->
      <div class="grid grid-cols-12 gap-4 mb-3 text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
        <div class="col-span-3">材料名称</div>
        <div class="col-span-2">类型</div>
        <div class="col-span-2 text-center">数量</div>
        <div class="col-span-2 text-center">单价 (¥)</div>
        <div class="col-span-2 text-center">金额 (¥)</div>
        <div class="col-span-1 text-center">操作</div>
      </div>

      <!-- 材料项容器 -->
      <div id="order-items-container" class="space-y-3">
        <!-- 材料项将通过JavaScript动态添加 -->
      </div>
      
      <!-- 总金额显示 -->
      <div class="order-total-section">
        <div class="order-total-display">
          <span class="order-total-label">
            <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            订单总金额：
          </span>
          <span id="order-total-amount" class="order-total-amount">¥0.00</span>
        </div>
      </div>
      
      <!-- 调试信息 -->
      <div class="mt-6 p-4 bg-gray-50 rounded-lg">
        <h3 class="font-semibold mb-2">调试信息：</h3>
        <div id="debug-info" class="text-sm text-gray-600">
          <p>材料项数量: <span id="item-count">0</span></p>
          <p>最后操作: <span id="last-action">无</span></p>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 模拟必要的全局函数
    function showError(message) {
      alert('错误: ' + message);
      updateDebugInfo('错误: ' + message);
    }
    
    function showSuccess(message) {
      alert('成功: ' + message);
      updateDebugInfo('成功: ' + message);
    }
    
    function updateDebugInfo(action) {
      document.getElementById('last-action').textContent = action;
      document.getElementById('item-count').textContent = document.getElementById('order-items-container').children.length;
    }

    /**
     * 添加订单材料项
     */
    function addOrderItem() {
      console.log('开始添加材料项...');
      const container = document.getElementById('order-items-container');
      if (!container) {
        console.error('Order items container not found');
        return;
      }

      const itemRow = document.createElement('div');
      itemRow.className = 'grid grid-cols-12 gap-4 mb-3 items-center p-3 bg-gray-50 rounded-lg border border-gray-200';

      itemRow.innerHTML = `
        <div class="col-span-3">
          <input type="text" class="form-control item-name" placeholder="输入材料名称" required>
        </div>
        <div class="col-span-2 relative">
          <input type="text" class="form-control item-type" placeholder="选择或输入类型">
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-quantity text-center" value="1" min="0" step="1" required>
        </div>
        <div class="col-span-2">
          <input type="number" class="form-control item-price text-center" value="0" min="0" step="0.01" required>
        </div>
        <div class="col-span-2 text-right">
          <span class="item-amount text-lg font-semibold text-gray-900">¥0.00</span>
        </div>
        <div class="col-span-1 text-center">
          <button type="button" class="delete-item p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      `;

      container.appendChild(itemRow);
      console.log('材料项HTML已添加到容器');

      // 添加事件监听器
      const quantityInput = itemRow.querySelector('.item-quantity');
      const priceInput = itemRow.querySelector('.item-price');
      const deleteButton = itemRow.querySelector('.delete-item');

      console.log('找到的元素:', {
        quantityInput: !!quantityInput,
        priceInput: !!priceInput,
        deleteButton: !!deleteButton
      });

      // 数量和价格变化时计算金额
      if (quantityInput) {
        quantityInput.addEventListener('input', () => calculateItemAmount(itemRow));
      }
      if (priceInput) {
        priceInput.addEventListener('input', () => calculateItemAmount(itemRow));
      }

      // 删除按钮点击时删除行
      if (deleteButton) {
        console.log('正在绑定删除按钮事件...');
        deleteButton.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          console.log('删除按钮被点击！');
          updateDebugInfo('删除按钮被点击');
          
          if (container.children.length > 1) {
            itemRow.remove();
            calculateOrderTotal();
            showSuccess('材料项已删除');
          } else {
            showError('订单至少需要一个材料项');
          }
        });
        console.log('删除按钮事件绑定完成');
      } else {
        console.error('未找到删除按钮！');
      }

      // 初始计算金额
      calculateItemAmount(itemRow);
      updateDebugInfo('添加了新的材料项');
    }

    /**
     * 计算单个材料项的金额
     */
    function calculateItemAmount(row) {
      const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
      const price = parseFloat(row.querySelector('.item-price').value) || 0;
      const amount = quantity * price;

      row.querySelector('.item-amount').textContent = `¥${amount.toFixed(2)}`;

      // 重新计算订单总金额
      calculateOrderTotal();
    }

    /**
     * 计算订单总金额
     */
    function calculateOrderTotal() {
      let total = 0;

      // 遍历所有材料项，累加金额
      document.querySelectorAll('#order-items-container .item-amount').forEach(element => {
        const amountText = element.textContent;
        const amount = parseFloat(amountText.replace('¥', '')) || 0;
        total += amount;
      });

      // 更新总金额显示
      const totalElement = document.getElementById('order-total-amount');
      if (totalElement) {
        totalElement.textContent = `¥${total.toFixed(2)}`;
      }
      
      updateDebugInfo('重新计算了总金额: ¥' + total.toFixed(2));
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      console.log('页面加载完成，开始初始化...');
      
      // 绑定添加材料项按钮
      const addItemBtn = document.getElementById('add-item-btn');
      if (addItemBtn) {
        addItemBtn.addEventListener('click', () => {
          console.log('添加材料项按钮被点击');
          addOrderItem();
        });
        console.log('添加按钮事件绑定完成');
      } else {
        console.error('未找到添加按钮！');
      }

      // 添加一个初始材料项
      addOrderItem();
      console.log('初始化完成');
    });
  </script>
</body>
</html>
